---
title: "R Notebook"
output: html_notebook
---

# Package Installation and Setup
```{r setup, include=FALSE}
# Install required packages if not already installed
if (!require(lme4)) install.packages("lme4")
if (!require(sjPlot)) install.packages("sjPlot")
if (!require(clubSandwich)) install.packages("clubSandwich")

library(lme4)
library(sjPlot)
library(clubSandwich)
```

# Data analysis
```{r Data analysis}

# Load the data file
load("dta1_20240903.RData")
# You can add a quick check to confirm the data loaded correctly

```

```{r Data analysis 2}

dta1$after_first_inspection[is.na(dta1$after_first_inspection)] = 0

# connection
#dta1$connections = rowSums(dta1[, c("NPC_sum_bool", "LPC_sum_bool", "LP_sum_bool", "NP_sum_bool", "LG_sum_bool", "NG_sum_bool", "PLA_sum_bool")])
#dta1$connections = rowSums(dta1[, c("NPC_sum_bool", "LPC_sum_bool", "LP_sum_bool", "NP_sum_bool", "LG_sum_bool", "NG_sum_bool")])

dta1$connections = rowSums(dta1[, c("LP_sum_bool", "NP_sum_bool", "LG_sum_bool", "NG_sum_bool")])

#dta1$connections = rowSums(dta1[, c("LP_sum_bool", "NP_sum_bool")])
dta1$connections_dummy <- NA
dta1[!is.na(dta1$connections) & dta1$connections > 0,"connections_dummy"] <- 1  #存在完全connections==NA的情况
dta1[!is.na(dta1$connections) & dta1$connections == 0,"connections_dummy"] <- 0

dta1$connection_num_all = rowSums(dta1[, c("NPC_sum", "LPC_sum", "LP_sum", "NP_sum", "LG_sum", "NG_sum", "PLA_sum")])


#inspection and ESG Rate

# create central connection and local connection
dta1$central_connection_dummy <- ifelse(dta1$NG_sum_bool == TRUE | dta1$NP_sum_bool == TRUE, TRUE, FALSE)
dta1$local_connection_dummy <- ifelse(dta1$LG_sum_bool == TRUE | dta1$LP_sum_bool == TRUE, TRUE, FALSE)

#dta1$central_connection_count <- rowSums(dta1[, c("NP_sum_bool", "NG_sum_bool")])
#dta1$local_connection_count <- rowSums(dta1[, c("LP_sum_bool", "LG_sum_bool")])

dta1$central_connection <- rowSums(dta1[, c("NP_sum", "NG_sum")])
dta1$local_connection <- rowSums(dta1[, c("LP_sum", "LG_sum")])
#dta1$central_connection <- dta1$NP_sum
#dta1$local_connection <- dta1$LP_sum

# create 人大政协 vs 政府
dta1$GOV_connection_dummy <- ifelse(dta1$NG_sum_bool == TRUE | dta1$LG_sum_bool == TRUE, TRUE, FALSE)
dta1$CPPCC_connection_dummy <- ifelse(dta1$NP_sum_bool == TRUE | dta1$LP_sum_bool == TRUE, TRUE, FALSE)

dta1$GOV_connection <- rowSums(dta1[, c("NG_sum", "LG_sum")])
dta1$CPPCC_connection <- rowSums(dta1[, c("NP_sum", "LP_sum")])
# create 地方人大政协 中央人大政协 地方政府 中央政府

# 连续型变量衡量connection
dta1$connection_num = rowSums(dta1[, c("LP_sum", "NP_sum", "LG_sum", "NG_sum")])


# model
colnames(dta1)[colnames(dta1) == "BloobergValue"] <- "Environmental_Information_Disclosure"

#save(dta1, file = "dta1.RData")   #

```

# Data Description 
```{r}
#load("D:/PPA/ESG_China/R/dta1.RData")
library(dplyr)
variables_of_interest <- dta1[, c("Environmental_Information_Disclosure", "connection_num", "central_connection", "local_connection", "after_first_inspection", "Age", "ROA", "ESG_Rate", "Leverage", "RegisterCapital_log", "SOE_new", "SOE_new_central", "SOE_new_local")]

variables_of_interest$connection_inspection <- with(variables_of_interest, after_first_inspection * connection_num)
variables_of_interest$central_connection_inspection <- with(dta1, after_first_inspection * central_connection)
variables_of_interest$local_connection_inspection <- with(dta1, after_first_inspection * local_connection)

variables_of_interest_filtered <- variables_of_interest %>%
  filter(!is.na(Environmental_Information_Disclosure))
variables_of_interest_NA <- variables_of_interest %>%
  filter(is.na(Environmental_Information_Disclosure))

```

```{r}

# 计算每个数值型变量的统计量
summary_stats <- lapply(variables_of_interest_filtered, function(x) {
  if (is.numeric(x)) {
    list(
      n = sum(!is.na(x)),
      mean = round(mean(x, na.rm = TRUE), 3),  # 保留三位小数
      sd = round(sd(x, na.rm = TRUE), 3),      # 保留三位小数
      max = round(max(x, na.rm = TRUE), 3),    # 保留三位小数
      min = round(min(x, na.rm = TRUE), 3)     # 保留三位小数
    )
  } else {
    NA
  }
})

# 将结果转换为数据框
summary_stats_df <- as.data.frame(do.call(rbind, summary_stats))

# 输出结果
print(summary_stats_df)

```

```{r}
library(ggplot2)
library(dplyr)

# 假设 variables_of_interest_filtered 已经是一个数据框，并且包含了你感兴趣的变量

# 你可以使用lapply结合ggplot2来为每个数值型变量生成图
# 首先，筛选出数值型变量
variables_of_interest_p <- dta1[, c("Environmental_Information_Disclosure", "connection_num", "central_connection", "local_connection", "after_first_inspection", "Age", "ROA", "ESG_Rate", "Leverage", "RegisterCapital_log")]
variables_of_interest_p <- variables_of_interest_p %>%
  filter(!is.na(Environmental_Information_Disclosure))

numeric_vars <- sapply(variables_of_interest_p, is.numeric)
numeric_var_names <- names(numeric_vars)[numeric_vars]

library(tidyr)

exclude_outliers1 <- function(x) {
  if (length(x) < 4) {  # 四分位数需要至少4个数据点
    warning("Too few observations to compute quartiles; returning NAs.")
    return(rep(NA, length(x)))
  }
  
  q1 <- quantile(x, probs = 0.25, na.rm = TRUE)
  q3 <- quantile(x, probs = 0.75, na.rm = TRUE)
  iqr <- q3 - q1
  lower_bound <- q1 - 1.5 * iqr
  upper_bound <- q3 + 1.5 * iqr
  
  # 使用逻辑索引来将超出范围的值替换为NA
  x[x < lower_bound | x > upper_bound] <- NA
  
  # 返回修改后的x
  return(x)
}


```
```{r}
exclude_outliers_percentile <- function(x, percentile = 5) {
  if (length(x) < 2) {  # 至少需要两个数据点
    warning("Too few observations; returning original data.")
    return(x)
  }
  
  cutoff <- quantile(x, probs = percentile / 100, na.rm = TRUE)
  
  # 使用逻辑索引来将超出范围的值替换为NA
  x[x > cutoff] <- NA
  
  # 返回修改后的x
  return(x)
}
  # 排除异常值
variables_of_interest_p_no_outliers <- variables_of_interest_p %>%
  mutate(
    ROA = exclude_outliers1(ROA),
    Leverage = exclude_outliers1(Leverage)
  )

# 使用列表解析进行名称替换
# 定义一个映射表
name_mapping <- list(
  "Environmental_Information_Disclosure" = "Environmental Information Disclosure",
  "connection_num" = "Connection",
  "central_connection" = "Central connection",
  "local_connection" = "Local connection",
  "after_first_inspection" = "Inspection",
  "ESG_Rate" = "Env Rate",
  "RegisterCapital_log" = "Register Capital (log)"
)

# 使用映射表修改变量名称
numeric_var_names_modified <- sapply(numeric_var_names, function(x) {
  if (x %in% names(name_mapping)) {
    return(name_mapping[[x]])
  } else {
    return(x)
  }
})
# 然后，为每个数值型变量生成一个图
plots <- lapply(numeric_var_names, function(var) {
  numeric_var_names_modified = numeric_var_names_modified[var]
  ggplot(variables_of_interest_p_no_outliers, aes_string(x = var)) +
    geom_histogram(bins = 30, color = "black", fill = "grey") +
    labs(x = numeric_var_names_modified, y = "Frequency") +
    theme_minimal()
})

library(gridExtra)
grid.arrange(grobs = plots[1:10], ncol = 5)  # 假设我们有足够的图来显示4个

```


### P1 connection (dummy)
```{r P1 connection (dummy)}
#P1

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + connections_dummy + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connections_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "p1_dummy.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Year", "Individual", "Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)

```


### P2 central/local connection (dummy)
```{r P2 central/local connection (dummy)}
#p2

library(lme4)

p2m2 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection_dummy * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection_dummy * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "p2_dummy.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)
```
### P3 connection (continuous)
```{r P3 connection (continuous)}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(lme4)
p3way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p3way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p3way1, p3way2, p3way3,
          file = "p3_continuous.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Year", "Individual", "Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)


```

### P4 contral/local connection (continuous)
```{r P4 contral/local connection (continuous)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)

p4m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p4m1, p4m2, p4m3, p4m4,
          file = "p4_continuous.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)
```

## 创建13类行业分类
```{r}
{r industry_classification_11}
# 创建13类行业分类
dta1$industry_type13 <- case_when(
  # 1. Agriculture, Forestry, Livestock Farming, Fishery
  dta1$IndustryName %in% c("农业", "林业", "畜牧业", "渔业", 
                           "农、林、牧、渔服务业") ~ "Agriculture, Forestry, Livestock Farming, Fishery",
  
  # 2. Mining
  dta1$IndustryName %in% c("煤炭开采和洗选业", "石油和天然气开采业", 
                           "黑色金属矿采选业", "有色金属矿采选业", 
                           "开采辅助活动") ~ "Mining",
  
  # 3. Manufacturing
  dta1$IndustryName %in% c("医药制造业", "化学原料及化学制品制造业", 
                           "化学纤维制造业", "非金属矿物制品业", 
                           "黑色金属冶炼及压延加工业", "有色金属冶炼及压延加工业", 
                           "金属制品业", "通用设备制造业", 
                           "专用设备制造业", "铁路、船舶、航空航天和其它运输设备制造业", 
                           "汽车制造业", "电气机械及器材制造业", 
                           "计算机、通信和其他电子设备制造业", "仪器仪表制造业", 
                           "其他制造业", "食品制造业", 
                           "酒、饮料和精制茶制造业", "农副食品加工业", 
                           "纺织业", "纺织服装、服饰业", 
                           "皮革、毛皮、羽毛及其制品和制鞋业", "木材加工及木、竹、藤、棕、草制品业", 
                           "造纸及纸制品业", "印刷和记录媒介复制业", 
                           "橡胶和塑料制品业", "家具制造业", 
                           "废弃资源综合利用业", "文教、工美、体育和娱乐用品制造业", 
                           "金属制品、机械和设备修理业", "石油加工、炼焦及核燃料加工业") ~ "Manufacturing",
  
  # 4. Electric Power, Gas, and Water Production and Supply
  dta1$IndustryName %in% c("电力、热力生产和供应业", "燃气生产和供应业", 
                           "水的生产和供应业") ~ "Electric Power, Gas, and Water Production and Supply",
  
  # 5. Construction
  dta1$IndustryName %in% c("房屋建筑业", "建筑安装业", 
                           "土木工程建筑业", "建筑装饰和其他建筑业") ~ "Construction",
  
  # 6. Transport and Storage
  dta1$IndustryName %in% c("装卸搬运和运输代理业", "道路运输业", 
                           "水上运输业", "铁路运输业", 
                           "航空运输业", "仓储业") ~ "Transport and Storage",
  
  # 7. Information Technology
  dta1$IndustryName %in% c("软件和信息技术服务业", "专业技术服务业","互联网和相关服务", 
                           "研究和试验发展", "科技推广和应用服务业") ~ "Information Technology",
  
  # 8. Wholesale and Retail Trade
  dta1$IndustryName %in% c("租赁业", "批发业", "零售业") ~ "Wholesale and Retail Trade",
  
  # 9. Finance and Insurance
  dta1$IndustryName %in% c("货币金融服务", "资本市场服务", 
                           "其他金融业", "保险业"
                           ) ~ "Finance and Insurance",
  
  # 10. Real Estate
  dta1$IndustryName %in% c("房地产业") ~ "Real Estate",
  
  # 11. Social Service
  dta1$IndustryName %in% c("教育", "卫生", 
                           "公共设施管理业", "生态保护和环境治理业", 
                           "居民服务业") ~ "Social Service",
  
  # 12. Communication and Culture
  dta1$IndustryName %in% c("电信、广播电视和卫星传输服务", "广播、电视、电影和影视录音制作业", 
                           "新闻和出版业", "文化艺术业", 
                           "体育", "邮政业") ~ "Communication and Culture",
  
  # 13. Others
  TRUE ~ "Others"
)



# 显示分类结果
cat("13类分类结果:\n")
industry_table <- table(dta1$industry_type13, useNA = "always")
print(industry_table)

# 分析NA值的详细情况
na_data <- dta1 %>% filter(is.na(industry_type13))
cat("\n=== NA值分析 ===\n")
cat("NA观测数:", nrow(na_data), "\n")
cat("NA占总观测数的比例:", round(nrow(na_data)/nrow(dta1)*100, 2), "%\n")
cat("NA涉及的唯一公司数:", length(unique(na_data$Symbol)), "\n")
cat("NA涉及的唯一行业数:", length(unique(na_data$IndustryName)), "\n")

# 显示所有未分类的行业名称
cat("\n未分类的行业名称:\n")
na_industries <- na_data %>%
  group_by(IndustryName) %>%
  summarise(count = n(), .groups = 'drop') %>%
  arrange(desc(count))
print(na_industries)

# 创建去除NA的数据集用于分析
dta1_13class <- dta1 %>% filter(!is.na(industry_type13))
cat("\n去除NA后的观测数:", nrow(dta1_13class), "\n")
cat("去除NA后的公司数:", length(unique(dta1_13class$Symbol)), "\n")
```



# Robustness test

### P5 contral + local connection (continuous)
Table B1. Compare the Effect Sizes of Central and Local Connection within One Model

```{r P5 contral + local connection (continuous)}

library(lme4)

p5m2 <- lmer(Environmental_Information_Disclosure ~ central_connection + local_connection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p5m4 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + local_connection * after_first_inspection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p5m2, p5m4,
          file = "B1.html",
          title = "Mixed-Effects Models",
          dv.labels = c("(1)", "(2)"),
          show.re.var = TRUE,
          show.icc = TRUE)
```



```{r P5 contral + local connection (continuous) province}

library(lme4)

p5m2 <- lmer(Environmental_Information_Disclosure ~ central_connection + local_connection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE), data=dta1)

p5m4 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + local_connection * after_first_inspection + ESG_Rate + Age + RegisterCapital_log + ROA + Leverage + (1|PROVINCE), data=dta1)

library(sjPlot)
tab_model(p5m2, p5m4,
          file = "B1_province.html",
          title = "Mixed-Effects Models",
          dv.labels = c("(1)", "(2)"),
          show.re.var = TRUE,
          show.icc = TRUE)
```


## lag
### lag t-1 data
```{r}
detach("package:plm", unload = TRUE)
library(dplyr)

# 假设dta1是你的数据框
# 对数据框按Symbol和EndYear排序，确保滞后计算正确
# 计算after_first_inspection的滞后项，并添加为新列after_first_inspection_lag

dta1 <- dta1 %>%
  arrange(Symbol, EndYear) %>%
  mutate(after_first_inspection_lag = lag(after_first_inspection, n = 2, default = NA))

dta1$after_first_inspection_lag
```

### P3 lag t-1
```{r P3 connection (continuous) lag}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection_lag * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "p3_lag.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Year", "Individual", "Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)


```
### P4 lag t-1
```{r P4 contral/local connection (continuous) lag}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)

p2m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "p4_lag.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)
```

### P1 SEO
```{r P1 SEO}
#P1

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + SOE + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * SOE + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "p1_seo.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Year", "Individual", "Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)

```


### P2 central/local SOE
```{r P2 central/local SOE}
#p2

library(lme4)

p2m2 <- lmer(Environmental_Information_Disclosure ~ SOE * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ SOE + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ SOE * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ SOE + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "p2_soe.html",
          title = "Mixed-Effects Models",
          dv.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)
```


### P3 SOE new dataset
Table B4. State-owned Enterprises
We labelled firms where the actual controllers are state-owned institutions or state-owned enterprises as politically connected. 
```{r P3 SOE new dataset 1}
#P3

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)

p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * SOE_new + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "B4_SOE_result_p1.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)

```


### P4 central/local SEO Level
```{r P4 central/local SEO level 0}
library(lme4)
p2m1 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m2 <- lmer(Environmental_Information_Disclosure ~ SOE_new_central * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ SOE_new_local + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new_local * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "B5_SOE_result_p2.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)
```

### P3 SOI
```{r P3 SOE new dataset}
#P3

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + SOI + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * SOI + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "SOI_result_p1.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)

```


### P4 central/local SOI Level
```{r P4 central/local SEO level 1}
#p4

library(lme4)
p2m1 <- lmer(Environmental_Information_Disclosure ~ SOI_central + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m2 <- lmer(Environmental_Information_Disclosure ~ SOI_central * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ SOI_local + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ Age + SOI_local * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "SOI_result_p2.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)
```

### P3 SOE strict
```{r P3 SOE new dataset 5}
#P3

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new2 + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * SOE_new2 + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "SOE_strict_result_p1.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)

```


### P4 central/local SEO Level
```{r P4 central/local SEO level 2}
#p4

library(lme4)
p2m1 <- lmer(Environmental_Information_Disclosure ~ SOE_central + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m2 <- lmer(Environmental_Information_Disclosure ~ SOE_central * after_first_inspection + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ SOE_new2_local + Age + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ Age + SOE_new2_local * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "SOE_strict_result_p2.html",
          title = "Mixed-Effects Models",
          show.re.var = TRUE,
          show.icc = TRUE)
```

### Testing for heteroskedasticity 协方差检验



```{r}
library(lmtest)
bptest(Environmental_Information_Disclosure ~ Age + connections_dummy +  ROA + ESG_Rate + Leverage + RegisterCapital_log +  + ESG_Rate, data = dta1, studentize=F)

```

### Cluster SE at province level
### Cluster SE at province level P1
```{r}
library(lme4)
library(lmerTest)  # For p-values in lmer
#P1

# Note: In lmer, clustering is handled through random effects structure
# The (1|PROVINCE/CITY) structure accounts for clustering at province/city level
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + connections_dummy + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)

p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connections_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# For robust standard errors with lmer, use:
# library(clubSandwich)
# coef_test(p1way1, vcov = "CR2", cluster = dta1$PROVINCE)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "cluster_se_p1.html",
          title = "Mixed-Effects Models with Clustered SE",
          dv.labels = c("Year", "Individual", "Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)


#t(sapply(c("HC0", "HC1", "HC2", "HC3", "HC4"), function(x) sqrt(diag(vcovHC(p1way1, type = x)))))

```
### Cluster SE at province level P2
```{r P2 central/local connection (dummy) Cluster SE}
#p2

library(lme4)

p2m2 <- lmer(Environmental_Information_Disclosure ~ central_connection_dummy * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ local_connection_dummy * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Note: For robust standard errors with lmer, use clubSandwich package
# c_p2m2 = coef_test(p2m2, vcov = "CR2", cluster = dta1$PROVINCE)
# c_p2m1 = coef_test(p2m1, vcov = "CR2", cluster = dta1$PROVINCE)
# c_p2m4 = coef_test(p2m4, vcov = "CR2", cluster = dta1$PROVINCE)
# c_p2m3 = coef_test(p2m3, vcov = "CR2", cluster = dta1$PROVINCE)


library(stargazer)
stargazer(c_p2m1, c_p2m2, c_p2m3, c_p2m4,
          type="text", 
          column.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)

```

### Cluster SE at province level P3
```{r P3 connection (continuous) Cluster}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure

# p3 model 1

library(lme4)
p3way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p3way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Cluster-robust standard errors using clubSandwich package
library(clubSandwich)
c_p3m1 = coef_test(p3way1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p3m2 = coef_test(p3way2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p3m3 = coef_test(p3way3, vcov = "CR2", cluster = dta1$PROVINCE)



library(sjPlot)
tab_model(p3way1, p3way2, p3way3,
          file = "P3_Cluster_SE.html",
          title = "Mixed-Effects Models with Clustered SE",
          dv.labels = c("Year", "Individual", "Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)

```

### Cluster SE at province level P4
```{r P4 contral/local connection (continuous) Cluster}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)

p4m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p4m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


# Cluster-robust standard errors using clubSandwich package
library(clubSandwich)
c_p4m1 = coef_test(p4m2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p4m2 = coef_test(p4m1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p4m3 = coef_test(p4m4, vcov = "CR2", cluster = dta1$PROVINCE)
c_p4m4 = coef_test(p4m3, vcov = "CR2", cluster = dta1$PROVINCE)



library(sjPlot)
tab_model(p4m1, p4m2, p4m3, p4m4,
          file = "P4_Cluster_SE.html",
          title = "Mixed-Effects Models with Clustered SE",
          dv.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)

```

### GMM
```{r}
library(dplyr)

# 假设dta1是你的数据框
# 对数据框按Symbol和EndYear排序，确保滞后计算正确
dta1 <- dta1 %>%
  arrange(Symbol, EndYear)

# 计算after_first_inspection的滞后项，并添加为新列after_first_inspection_lag
dta1 <- dta1 %>%
  mutate(after_first_inspection_lag = lag(after_first_inspection))
```


### P1 GMM
```{r P1 GMM}
#P1
library(gmm)

# 定义模型的因变量和解释变量
y <- dta1$Environmental_Information_Disclosure
x <- model.matrix(~ Age + connections_dummy + ROA + ESG_Rate + Leverage + 
                  RegisterCapital_log + as.factor(IndustryName) + 
                  as.factor(PROVINCE), data = dta1)

# 假设所有连续变量为工具变量
tools <- X[, c("Age", "ROA", "ESG_Rate", "Leverage", "RegisterCapital_log")]


# 自定义g函数
g_fn <- function(theta, x) {
  resid <- residuals(lm(y ~ x, data = data.frame(y = y, x = X)))
  return(t(resid))
}

# 使用gmm函数进行GMM估计，使用自定义的g函数
fit_gmm <- gmm(g_fn, data = dta1, instruments = tools, 
               index = list(c("Symbol", "EndYear")), 
               model = "within", effect = "twoways")

# 查看模型结果
summary(fit_gmm)

# 使用gmm函数进行GMM估计
fit_gmm <- gmm(g_fn, x = X, instruments = tools, y = y, index = list(c("Symbol", "EndYear")), 
               model = "within", effect = "twoways")

# 查看模型结果
summary(fit_gmm)

library(lme4)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + connections_dummy + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection_lag * connections_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "gmm_p1.html",
          title = "Mixed-Effects Models (GMM Section)",
          dv.labels = c("Year", "Individual", "Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)





```

### P2 GMM
```{r P2 GMM}
library(lme4)

p2m2 <- lmer(Environmental_Information_Disclosure ~ central_connection_dummy * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ local_connection_dummy * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection_dummy + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)


library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "gmm_p2.html",
          title = "Mixed-Effects Models (GMM Section)",
          dv.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)
```
### P3 GMM
```{r P3 GMM}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(lme4)
library(clubSandwich)
p1way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p1way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection_lag * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Cluster-robust standard errors
c_p1way1 <- coef_test(p1way1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p1way2 <- coef_test(p1way2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p1way3 <- coef_test(p1way3, vcov = "CR2", cluster = dta1$PROVINCE)

library(sjPlot)
tab_model(p1way1, p1way2, p1way3,
          file = "gmm_p3.html",
          title = "Mixed-Effects Models (P3 GMM)",
          dv.labels = c("Year", "Individual", "Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)


```
### P4 GMM
```{r P4 GMM}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1

library(lme4)
library(clubSandwich)

p2m2 <- lmer(Environmental_Information_Disclosure ~ central_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m4 <- lmer(Environmental_Information_Disclosure ~ local_connection * after_first_inspection_lag + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p2m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

# Cluster-robust standard errors
c_p2m1 <- coef_test(p2m1, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m2 <- coef_test(p2m2, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m3 <- coef_test(p2m3, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m4 <- coef_test(p2m4, vcov = "CR2", cluster = dta1$PROVINCE)

library(sjPlot)
tab_model(p2m1, p2m2, p2m3, p2m4,
          file = "gmm_p4.html",
          title = "Mixed-Effects Models (P4 GMM)",
          dv.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)
```


## Selection bias

### P1 Selection bias
```{r P1 Selection bias}
#P1
library(dplyr)

dta1$Environmental_Information_Disclosure_binary <- 
  ifelse(is.na(dta1$Environmental_Information_Disclosure), 0, 1)

library(lme4)
# Note: For binary outcomes, consider using glmer() with family=binomial
p1way1_binary <- glmer(
  Environmental_Information_Disclosure_binary ~
    Age + connections_dummy + ROA + Leverage +
    RegisterCapital_log + (1|PROVINCE/CITY),
  data = dta1,
  family = binomial
)


p1way2_binary <- glmer(
  Environmental_Information_Disclosure_binary ~
    after_first_inspection + RegisterCapital_log +
    ROA + Leverage + (1|PROVINCE/CITY),
  data = dta1,
  family = binomial
)


p1way3_binary <- glmer(
  Environmental_Information_Disclosure_binary ~
    Age + after_first_inspection + connections_dummy +
    RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY),
  data = dta1,
  family = binomial
)

library(sjPlot)
tab_model(p1way1_binary, p1way2_binary, p1way3_binary,
          file = "selection_bias_p1.html",
          title = "Mixed-Effects Models (Binary Outcome)",
          dv.labels = c("Year", "Individual", "Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)

```

### P2 Selection bias
```{r P2 Selection bias}
library(lme4)
library(clubSandwich)

p2m2_binary <- glmer(
  Environmental_Information_Disclosure_binary ~
    central_connection_dummy + after_first_inspection +
    RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY),
  data = dta1,
  family = binomial
)

p2m1_binary <- glmer(
  Environmental_Information_Disclosure_binary ~
    Age + central_connection_dummy +
    RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY),
  data = dta1,
  family = binomial
)


p2m4_binary <- glmer(
  Environmental_Information_Disclosure_binary ~
    local_connection_dummy + after_first_inspection +
    RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY),
  data = dta1,
  family = binomial
)


p2m3_binary <- glmer(
  Environmental_Information_Disclosure_binary ~
    Age + local_connection_dummy +
    RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY),
  data = dta1,
  family = binomial
)

# Cluster-robust standard errors for binary models
c_p2m1_binary <- coef_test(p2m1_binary, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m2_binary <- coef_test(p2m2_binary, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m3_binary <- coef_test(p2m3_binary, vcov = "CR2", cluster = dta1$PROVINCE)
c_p2m4_binary <- coef_test(p2m4_binary, vcov = "CR2", cluster = dta1$PROVINCE)

library(sjPlot)
tab_model(p2m1_binary, p2m2_binary, p2m3_binary, p2m4_binary,
          file = "selection_bias_p2.html",
          title = "Mixed-Effects Models (P2 Selection Bias)",
          dv.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          show.re.var = TRUE,
          show.icc = TRUE)
```

### P3 Selection bias
```{r P3 Selection bias}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(plm)
p1way1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + connection_num + ROA + Leverage + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)



p1way2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    after_first_inspection + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p1way3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + after_first_inspection + connection_num + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


library(stargazer)
stargazer(p1way1_binary, p1way2_binary, p1way3_binary,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


```

### P4 Selection bias
```{r P4 contral/local connection (continuous) Selection bias}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p2m2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    central_connection + after_first_inspection + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p2m1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + central_connection + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + local_connection + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m4_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    local_connection + after_first_inspection + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)



library(stargazer)
stargazer(p2m1_binary, p2m2_binary, p2m3_binary,p2m4_binary,
          type="text", 
          column.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```


## Selection bias (lag)
```{r}
library(dplyr)

# 假设dta1是你的数据框
# 对数据框按Symbol和EndYear排序，确保滞后计算正确
dta1 <- dta1 %>%
  arrange(Symbol, EndYear)

# 计算after_first_inspection的滞后项，并添加为新列after_first_inspection_lag
dta1 <- dta1 %>%
  mutate(after_first_inspection_lag = lag(after_first_inspection))
```

### P1 Selection bias (lag)
```{r P1 Selection bias (lag)}
#P1
dta1$Environmental_Information_Disclosure_binary <- 
  ifelse(is.na(dta1$Environmental_Information_Disclosure), 0, 1)

library(plm)
p1way1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + connections_dummy + ROA + ESG_Rate + Leverage + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE),
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p1way2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    after_first_inspection_lag + ESG_Rate + RegisterCapital_log + 
    ROA + Leverage + as.factor(IndustryName) + as.factor(PROVINCE),
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p1way3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + after_first_inspection_lag * connections_dummy + 
    as.factor(IndustryName) + as.factor(PROVINCE) + ESG_Rate + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within"
)

library(stargazer)
stargazer(p1way1_binary, p1way2_binary, p1way3_binary,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)

```

### P2 Selection bias (lag)
```{r P2 Selection bias (lag)}
library(plm)

p2m2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    central_connection_dummy * after_first_inspection_lag + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)

p2m1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + central_connection_dummy + ESG_Rate + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m4_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    local_connection_dummy * after_first_inspection_lag + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p2m3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + local_connection_dummy + ESG_Rate + 
    RegisterCapital_log + ROA + as.factor(IndustryName) + 
    as.factor(PROVINCE) + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)



library(stargazer)
stargazer(p2m1_binary, p2m2_binary, p2m3_binary,p2m4_binary,
          type="text", 
          column.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```

### P3 Selection bias (lag)
```{r P3 Selection bias (lag)}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(plm)
p1way1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + connection_num + ROA + ESG_Rate + Leverage + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)



p1way2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    after_first_inspection_lag + ESG_Rate + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p1way3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + after_first_inspection_lag * connection_num + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


library(stargazer)
stargazer(p1way1_binary, p1way2_binary, p1way3_binary,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


```

### P4 Selection bias (lag)
```{r P4 contral/local connection (continuous) Selection bias (lag)}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p2m2_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    central_connection * after_first_inspection_lag + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)


p2m1_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + central_connection + ESG_Rate + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m3_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    Age + local_connection + ESG_Rate + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol", "EndYear"),
  model = "within",
  effect = "twoways"
)


p2m4_binary <- plm(
  Environmental_Information_Disclosure_binary ~ 
    local_connection * after_first_inspection_lag + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1,
  index = c("Symbol"),
  model = "within"
)



library(stargazer)
stargazer(p2m1_binary, p2m2_binary, p2m3_binary,p2m4_binary,
          type="text", 
          column.labels = c("Central: Individual", "Central: Two Ways", "Local: Individual", "Local: Two Ways"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```



## lm
```{r}
library(dplyr)
```
### P1 lm
```{r P1 lm}
#P1
p1way1_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + connections_dummy + ROA + ESG_Rate + Leverage + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE),
  data = dta1
)


p1way2_binary <- lm(
  Environmental_Information_Disclosure ~ Age + 
    after_first_inspection + ESG_Rate + RegisterCapital_log + 
    ROA + Leverage + as.factor(IndustryName) + as.factor(PROVINCE),
  data = dta1
)


p1way3_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + after_first_inspection * connections_dummy + 
    as.factor(IndustryName) + as.factor(PROVINCE) + ESG_Rate + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1
)

library(stargazer)
stargazer(p1way1_binary, p1way2_binary, p1way3_binary,
          type="text", 
          column.labels = c("Year", "Individual", "Two Ways"),
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)

```

### P2 lm
```{r P2 lm}
library(plm)

p2m2_binary <- lm(
  Environmental_Information_Disclosure ~ Age + 
    central_connection_dummy * after_first_inspection + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1
)

p2m1_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + central_connection_dummy + ESG_Rate + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1
)


p2m4_binary <- lm(
  Environmental_Information_Disclosure ~ Age + 
    local_connection_dummy * after_first_inspection + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1
)


p2m3_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + local_connection_dummy + ESG_Rate + 
    RegisterCapital_log + ROA + as.factor(IndustryName) + 
    as.factor(PROVINCE) + Leverage,
  data = dta1
)



library(stargazer)
stargazer(p2m1_binary, p2m2_binary, p2m3_binary,p2m4_binary,
          type="text", 
          column.labels = c("Central", "Central Ways", "Local", "Local"),
          title="Fixed-Effects",
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```

### P3 lm
```{r P3 lm}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

p1way1_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + connection_num + ROA + ESG_Rate + Leverage + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log,
  data = dta1
)



p1way2_binary <- lm(
  Environmental_Information_Disclosure ~ Age + 
    after_first_inspection + ESG_Rate + 
    as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1
)


p1way3_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + after_first_inspection * connection_num + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage,
  data = dta1
)


library(stargazer)
stargazer(p1way1_binary, p1way2_binary, p1way3_binary,
          type="text", 
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)


```

### P4 lm
```{r P4 contral/local connection (continuous) lm}

#P4: The effects of central and local political connection (continuous) and policy pressure on environmental information disclosure

# p4 model 1   

library(plm)

p2m2_binary <- lm(
  Environmental_Information_Disclosure ~ Age + 
    central_connection * after_first_inspection + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1
)


p2m1_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + central_connection + ESG_Rate + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage + ESG_Rate,
  data = dta1
)


p2m3_binary <- lm(
  Environmental_Information_Disclosure ~ 
    Age + local_connection + ESG_Rate + 
    RegisterCapital_log + as.factor(IndustryName) + 
    as.factor(PROVINCE) + ROA + Leverage + ESG_Rate,
  data = dta1
)


p2m4_binary <- lm(
  Environmental_Information_Disclosure ~ Age + 
    local_connection * after_first_inspection + 
    ESG_Rate + as.factor(IndustryName) + as.factor(PROVINCE) + 
    RegisterCapital_log + ROA + Leverage + ESG_Rate,
  data = dta1
)



library(stargazer)
stargazer(p2m1_binary, p2m2_binary, p2m3_binary,p2m4_binary,
          type="text", 
          column.labels = c("Central", "Central", "Local", "Local"),
          omit="as.factor",
          notes=c("nill"),
          dep.var.labels = "ESG"
)
```

## Inspected 后三年标记为1, inspection当年标记为0
We assign a code of '1' to firms within the three-year period following their initial inspection to assess the short-term effects of regulatory pressure.
Table B2. Short-term Impact of Inspection  

```{r}
# # 创建一个新的数据框，只包含所需的列
# new_df <- select(dta1, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)

# na_rows <- dta1 %>% filter(is.na(first_inspection))
# new_df <- select(na_rows, Symbol, EndYear, inspection_year, first_inspection, after_first_inspection, extended_inspection)

```
### P3 connection (continuous)
```{r}
# 加载必要的库
library(lubridate)
library(dplyr)
library(tidyr)

dta3 = dta1
dta3 <- dta3 %>%
  # 将 Endyear 转换为日期格式
  mutate(EndYear = ymd(EndYear)) %>%
  # 提取年份并存储在 new_year 变量中
  mutate(Year = year(EndYear))

# 创建新变量 extended_inspection
dta3 <- dta3 %>%
  group_by(Symbol) %>%  # 按照单位分组
  mutate(
    # 找到每个单位 first_inspection 为1的年份
    inspection_year = ifelse(first_inspection == 1, Year, NA),
    # 使用 coalesce 处理 NA 值，确保每个单位都有一个 inspection_year
    inspection_year = coalesce(inspection_year, lag(inspection_year, default = NA))
  ) %>%
  # 使用 fill 函数确保 inspection_year 被赋值到所有行
  fill(inspection_year, .direction = "downup") %>%
  ungroup()  # 解除分组

# 标记 inspection_year 及之后的两年
dta3$extended_inspection <- as.numeric(dta3$Year > dta3$inspection_year & dta3$Year <= (dta3$inspection_year + 3))
dta3 <- dta3 %>% mutate(extended_inspection = ifelse(is.na(extended_inspection), 0, extended_inspection))
#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure

library(lme4)
p3way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta3)


p3way2 <- lmer(Environmental_Information_Disclosure ~ Age + extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + extended_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

library(sjPlot)
tab_model(p3way1, p3way2, p3way3,
          file = "B2_p3_extended_inspection.html",
          title = "Mixed-Effects Models (Extended Inspection)",
          #dv.labels = c("Year", "Individual", "Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)


```

### P4 contral/local connection (continuous)
Table B3. Short-term Impact of Inspection (central and local connections)
```{r}

library(lme4)

p4m1 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4m2 <- lmer(Environmental_Information_Disclosure ~ Age + central_connection * extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4m3 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

p4m4 <- lmer(Environmental_Information_Disclosure ~ Age + local_connection * extended_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta3)

library(sjPlot)
tab_model(p4m1, p4m2, p4m3, p4m4,
          file = "B3_p4_extended_inspection.html",
          title = "Mixed-Effects Models (Extended Inspection)",
          #dv.labels = c("Central: Two Ways", "Central: Individual", "Local: Two Ways", "Local: Individual"),
          show.re.var = TRUE,
          show.icc = TRUE)
```

# Data visualization

### connection before vs after (predicted value)
```{r}
## load data
#load("F:/PPA/ESG_China/R/dta1.RData")
#load("D:/PPA/ESG_China/R/dta1.RData")
```
### SOE Distribution
```{r}
# 加载 ggplot2 包
library(ggplot2)

ggplot(dta1, aes(x = SOE)) +
  geom_histogram(binwidth = 10, position = "dodge") +  # 设置柱状图宽度和分组位置
  labs(title = "SOE 分布的直方图按 Symbol 分组",
       x = "SOE 百分比",
       y = "频数") +
  theme_minimal()
```
```{r}
library(ggplot2)

# 假设 dta1 数据集已经加载
mean_data <- dta1 %>% 
  group_by(Year) %>% 
  summarise(mean_SOE = mean(SOE), .groups = 'drop')

# 绘制柱状图并添加平均值的虚线和文本
ggplot(dta1, aes(x = SOE)) + 
  geom_histogram(position = "dodge", fill = "grey", color = "grey") +
  labs(title = "Histograms of the SOE Distribution Grouped by Year", 
       x = "SOE Percentage", 
       y = "Frequency") + 
  theme_minimal() + 
  facet_wrap(~ Year) +
  geom_vline(data = mean_data, aes(xintercept = mean_SOE), color = "black", linetype = "dashed", size = 1) +
  # 添加文本
  geom_text(data = mean_data, aes(x = mean_SOE, y = Inf, label = round(mean_SOE, 2)), 
            vjust = 1.5, color = "black", size = 3.5, angle = 90, hjust = 1.1) +
  # 调整图表标题的水平居中
  theme(plot.title = element_text(hjust = 0.5))
```

### Connection Distribution

```{r}
# 加载 ggplot2 和 dplyr 包
library(ggplot2)
library(dplyr)

# 计算每个年份的平均SOE值
mean_data <- dta1 %>% 
  group_by(Year) %>% 
  summarise(mean_connection = mean(connection_num, na.rm = TRUE), .groups = 'drop')

# 绘制柱状图并添加平均值的虚线和文本
ggplot(dta1, aes(x = connection_num)) + 
  geom_histogram(position = "dodge", fill = "grey", color = "grey") +
  labs(title = "Histograms of Connection Numbers Grouped by Year",
       x = "Connection Number",
       y = "Frequency") + 
  theme_minimal() + 
  facet_wrap(~ Year) +
  geom_vline(data = mean_data, aes(xintercept = mean_connection), color = "black", linetype = "dashed", size = 1) +
  # 添加文本
  geom_text(data = mean_data, aes(x = mean_connection, y = Inf, label = round(mean_connection, 2)), 
            vjust = 1.5, color = "black", size = 3.5, angle = 90, hjust = 1.1) +
  # 调整图表标题的水平居中
  theme(plot.title = element_text(hjust = 0.5))
```


```{r connection before vs after (predicted value)}
# 1. Fit your regression model using lmer:
library(lme4)
p1way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data = dta1)

# Extract random effects (equivalent to fixef in plm)
ranef(p1way3)
summary(ranef(p1way3))


#检验是否一致
p1way3.t <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data = dta2)


#library(sjPlot)
#tab_model(p1way3, p1way3.t,
#          file = "prediction_comparison.html",
#          title = "Mixed-Effects Models Comparison",
#          dv.labels = c("full dataset", "filtered dataset"),
#          show.re.var = TRUE,
#          show.icc = TRUE)


#2. Extract the predicted values from the model:
# For lmer, use predict() function
df_predicted = predict(p1way3)
df_predicted = as.data.frame(df_predicted)


#合并数据这里存在问题，需要进一步研究
rownames(df_predicted) = as.numeric(rownames(df_predicted))
# 确保 dta1 的行名与 df_predicted 的索引一致
if(all(rownames(df_predicted) %in% rownames(dta1))){
  # 合并数据框
  dta2 <- dta1
  dta2$predicted_value <- df_predicted$df_predicted[as.numeric(rownames(dta2))]
}
dta2$predicted_value[is.na(dta2$predicted_value)] <- NA


dta2 =merge(df_predicted, dta1, by = "row.names", all.x = TRUE)
dta2$Row.names = as.numeric(dta2$Row.names)


rownames(df_predicted) <- seq_len(nrow(df_predicted)) # 确保两者的行名是连续的整数
rownames(dta1) <- seq_len(nrow(dta1))
# 合并数据框
result <- cbind(dta1, df_predicted[, -1]) # 使用 cbind() 函数结合两个数据框，-1 表示不包含第一个列（假设这是索引列）


## 计算置信区间
# 计算预测值
predicted_values <- predict(p1way3)

# 获取残差的标准误
residuals <- residuals(p1way3, type = "within")
std_error <- sd(residuals)

# 计算 t 分布的临界值（这里假设95%置信水平）
confidence_level <- 0.95
critical_value <- qt(p = (1 - confidence_level) / 2, df = p1way3$df.residual)

# 计算预测值的置信区间宽度
margin_of_error <- critical_value * std_error * sqrt(1 / nobs(p1way3))

# 计算上界和下界
lower_ci <- predicted_values - margin_of_error
upper_ci <- predicted_values + margin_of_error
lower_ci = as.data.frame(lower_ci)
upper_ci = as.data.frame(upper_ci)

dta2 =merge(lower_ci, dta2, by = "row.names", all.x = TRUE)
dta2 =merge(upper_ci, dta2, by = "row.names", all.x = TRUE)

dta3 = dta2
#3. Create a data frame with the predicted values:
dta2$connection_level = cut(dta2$connection_num, breaks = c(-1, 1, 2, 4, 6, Inf), labels = c(0, 1, 2, 3, 4), right=FALSE)

df_after0 <- subset(dta2, after_first_inspection == 0)
df_after1 <- subset(dta2, after_first_inspection == 1)
```
```{r vis}
# 加载 ggplot2 库
library(ggplot2)

# 假设原始数据框 df 有 after_first_inspection 和 connection_level 列，
# 并且我们已经根据 after_first_inspection 创建了 df_after0 和 df_after1。

# 在 df_after0 和 df_after1 数据框中分别添加一个表示组别的新列（例如：inspection_status）
df_after0$inspection_status <- "Before"
df_after1$inspection_status <- "After"

# 合并两个数据框以创建新的绘图数据集
df_plot <- rbind(df_after0, df_after1)
df_plot$inspection_status <- factor(df_plot$inspection_status, levels = c("Before", "After"))
# 创建条形图并设置一个图例
p <- ggplot(df_plot, aes(x = connection_level, y = df_predicted, fill = inspection_status)) +
  geom_bar(stat = "identity", position = "dodge") +
  
  # 根据 inspection_status 设置子图标题
  labs(title = "Distribution of Predicted Values by Connection Level and Inspection Status",
       fill = "Inspection Status") +
  # 添加 x轴和y轴标签
  xlab("Connection Level") +
  ylab("Predicted Value") +

  # 调整布局为两列，并添加图例
  #facet_wrap(~inspection_status, ncol = 1) +
  theme(legend.position = "bottom")

# 显示图形
print(p)
```


```{r}
# 假设 df_plot 包含以下列：connection_level, df_predicted, lower_ci, upper_ci
# 并且 inspection_status 列已存在，用于区分不同的子图
df_predicted <- predict(p1way3)

# 计算残差的标准误
residuals <- residuals(p1way3, type = "within")
std_error <- sd(residuals)

# 假设我们想要95%的置信区间
confidence_level <- 0.95
critical_value <- qt(p = (1 + confidence_level) / 2, df = p1way3$df.residual)

# 计算置信区间
margin_of_error <- critical_value * std_error * sqrt(1 / p1way3$nobs)
lower_ci <- df_predicted - margin_of_error
upper_ci <- df_predicted + margin_of_error


# 创建条形图并添加置信区间
p <- ggplot(df_plot, aes(x = connection_level)) +
  geom_bar(aes(y = df_predicted, fill = inspection_status), stat = "identity", position = "dodge") +
  geom_errorbar(aes(ymin = lower_ci, ymax = upper_ci, color = inspection_status), width = 0.25, position = position_dodge(width = 0.9)) +
  
  labs(title = "Distribution of Predicted Values with Confidence Intervals by Connection Level and Inspection Status",
       fill = "Inspection Status", color = "Inspection Status") +
  
  xlab("Connection Level") +
  ylab("Predicted Value (with 95% CI)") +
  
  facet_wrap(~inspection_status, ncol = 1) +
  theme(legend.position = "top")

print(p)

```


```{r}

# Draw a bar plot with the predicted values for the two subplots:
library(ggplot2)

# Create the bar plot with two subplots
p <- ggplot() +
  # Subplot 1: after_first_inspection = 0
  geom_bar(data = df_after0, aes(x = connection_level, y = df_predicted), stat = "identity", fill = "blue", position = "dodge") +
  labs(title = "after_first_inspection = 0") +
  
  # Subplot 2: after_first_inspection = 1
  geom_bar(data = df_after1, aes(x = connection_level, y = df_predicted), stat = "identity", fill = "red", position = "dodge") +
  labs(title = "after_first_inspection = 1") +
  
  # Adjust the layout
  facet_wrap(~., ncol = 2)

# Display the plot
print(p)
```
```{r}
library(ggplot2)

p <- ggplot(data = dta2) +
  geom_bar(aes(x = connection_num, y = df_predicted), stat = "summary", position = "dodge", fun="mean") +
  facet_wrap(~ after_first_inspection, ncol = 2) +
  labs(title = "")

print(p)
```
```{r}


ggplot(dta2, aes(x=connection_num, y=df_predicted)) +
  geom_point() +
  facet_wrap( ~ after_first_inspection) 
```
```{r}


library(lme4)
library(clubSandwich)
p1way4 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data = dta1)

# Cluster-robust standard errors
c_p1way4 <- coef_test(p1way4, vcov = "CR2", cluster = dta1$PROVINCE)

dta2.p <- pdata.frame(dta2, index = c("Symbol"))

library(sjPlot)
plot_model(p1way3.t, type = "pred", terms = c("after_first_inspection", "connection_num")) 
plot_model(p1way3.t, type = "int")
```
```{r}
predictions(p1way4, by = "connection_num")

plot_predictions(p1way4, condition = c("connection_num", "after_first_inspection"))
```

```{r}
library(plyr)
# compute mean and sd per combination of wool & tension
dta2$connection_level = cut(dta2$connection_num, breaks = c(-1, 2, 4, 6,8, Inf), labels = c("0-1", "2-3", "4-5", "6-7","8-Inf"), right=FALSE)

df <- ddply(dta2, c("connection_level", "after_first_inspection"), summarize, Mean = mean(df_predicted), SD = sd(df_predicted))
```

```{r}
ggplot(df, aes(x = connection_level, y = Mean)) +
    geom_bar(stat = "identity") + facet_wrap(.~after_first_inspection) +
    ggtitle("Before and after inspection") + ylab("Predicted Value of ESG disclosure (mean)") +
    # add 68% CI errorbar 
    geom_errorbar(aes(ymin = Mean - 1.96 * SD, ymax = Mean + 1.96 * SD), width = 0.2, position = position_dodge(0.9))
```



### Predicted value of P3 
Figure 2 Predicted Value of Environmental Information Disclosure
effect_plot
```{r}

#P3: The effects of political connection (continous) and policy pressure on environmental information disclosure


# p3 model 1

library(lme4)
p3way1 <- lmer(Environmental_Information_Disclosure ~ Age + connection_num + ROA + ESG_Rate + Leverage + RegisterCapital_log + (1|PROVINCE/CITY), data=dta1)


p3way2 <- lmer(Environmental_Information_Disclosure ~ after_first_inspection + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)

p3way3 <- lmer(Environmental_Information_Disclosure ~ Age + after_first_inspection * connection_num + ESG_Rate + RegisterCapital_log + ROA + Leverage + (1|PROVINCE/CITY), data=dta1)
```

```{r}
# 假设已经根据dta1数据集中的Symbol和EndYear创建了必要的哑变量或者决定只考虑行业和省份效应
# 注意：实际操作中，需要先执行哑变量编码，这里简化处理
dta2 = dta1
dta2$Symbol = as.factor(dta2$Symbol)
dta2$PROVINCE = as.factor(dta2$PROVINCE)
dta2$IndustryName = as.factor(dta2$IndustryName)

# 使用lm函数构建模型
lm_model <- lm(Environmental_Information_Disclosure ~ 
                 Age + after_first_inspection * connection_num + ESG_Rate + 
                 PROVINCE + IndustryName +
                 RegisterCapital_log + ROA + Leverage + ESG_Rate, 
               data = dta1)
```

```{r}
library(jtools)
p3way3_v = effect_plot(lm_model, pred = after_first_inspection, interval = TRUE, plot.points = FALSE, 
            jitter = 0.05, y.label = "After Inspection", main = "Predicted Effect of Inspection")

p3way3_v
```

```{r}
library(sjPlot)
library(sjmisc)
library(ggplot2)
plot_model(lm_model, type = "pred", terms = c("connection_num", "after_first_inspection"))

```
```{r}
plot_model(
  lm_model, 
  type = "pred", 
  terms = c("connection_num", "after_first_inspection"),
  legend.title = "Inspection",
  axis.labels = c(connection_num = "Connection", Environmental_Information_Disclosure = "Environmental Information Disclosure"),
  # 添加图表标题
  title = "Predicted Value of Environmental Information Disclosure by Connection and Inspection"
)
```

```{r p3 final version}
#dta2 = dta1
#dta2$Symbol = as.factor(dta2$Symbol)
#dta2$PROVINCE = as.factor(dta2$PROVINCE)
#dta2$IndustryName = as.factor(dta2$IndustryName)

# 使用lm函数构建模型
lm_model_p3 <- lm(Environmental_Information_Disclosure ~ 
                 Age + after_first_inspection * connection_num + ESG_Rate + 
                 PROVINCE + IndustryName +
                 RegisterCapital_log + ROA + Leverage, 
               data = dta1)

#library(sjlabelled)
library(ggeffects)
library(ggplot2)
p3_interaction <- predict_response(lm_model_p3, c("connection_num", "after_first_inspection"))
ggplot(p3_interaction, aes(x = x, y = predicted, colour = group)) +
  geom_line(aes(colour = group)) + 
  geom_ribbon(aes(ymin = conf.low, ymax = conf.high), alpha = 0.3, fill="grey70", linetype=0) + 
  scale_colour_manual(values = c("grey70", "black")) +  # 自定义线条颜色为灰色和黑色
  labs(  # 图表标题
       x = "Connection",                      # x轴标签
       y = "Environmental Information Disclosure",  # y轴标签
       colour = "Inspection Type",                   # 更新图例标题以更清晰表达
       fill = "Inspection Type")                     # 填充图例标题，确保与线条图例一致，可选

```
```{r}
ggplot(p3_interaction, aes(x = x, y = predicted, colour = group)) +
  geom_line(size = 1) +                                   # 绘制不同组别的线条
  geom_point(size = 2, alpha = 0.6) +                     # 添加散点以突出每个数据点
  scale_colour_manual(values = c("#0072B2", "#D55E00"),   # 自定义颜色，这里举例使用蓝色和橙色，您可以按需调整
                      labels = c("Before Inspection", "After First Inspection")) +
  labs(title = "Interaction Effects between Connection and Inspection",  # 更新标题以强调交互效应
       x = "Connection Number",                                          # x轴标签保持
       y = "Predicted Environmental Information Disclosure",            # 更新y轴标签以完整表述预测值
       colour = "Inspection Timing")                                      # 图例标题更改为"Inspection Timing"以明确

  theme_minimal() +                                             # 使用极简主题以便聚焦数据
  theme(legend.position = "bottom")                              # 图例位置调整至底部，便于观看

```
### Predicted value of P4
Figure 3 Predicted Value of Environmental Information Disclosure (Central connection v.s. Local connection)
```{r p4 final version}
#dta3 = dta1
#dta3$Symbol = as.factor(dta3$Symbol)
#dta3$PROVINCE = as.factor(dta3$PROVINCE)
#dta3$IndustryName = as.factor(dta3$IndustryName)

# 使用lm函数构建模型
lm_model_p4_1 <- lm(Environmental_Information_Disclosure ~ 
                 Age + after_first_inspection * central_connection + ESG_Rate + 
                 PROVINCE + IndustryName +
                 RegisterCapital_log + ROA + Leverage, 
               data = dta1)

lm_model_p4_2 <- lm(Environmental_Information_Disclosure ~ 
                 Age + after_first_inspection * local_connection + ESG_Rate + 
                 PROVINCE + IndustryName +
                 RegisterCapital_log + ROA + Leverage, 
               data = dta1)

#library(sjlabelled)
library(ggeffects)
library(ggplot2)
# 为模型1创建预测响应并添加type列
p4_interaction_1 <- predict_response(lm_model_p4_1, c("central_connection", "after_first_inspection"))
p4_interaction_1$type <- "Central"
# 为模型2创建预测响应并添加type列
p4_interaction_2 <- predict_response(lm_model_p4_2, c("local_connection", "after_first_inspection"))
p4_interaction_2$type <- "Local"
# 合并预测响应
p4_interaction <- rbind(p4_interaction_1, p4_interaction_2)


ggplot(p4_interaction, aes(x = x, y = predicted, colour = group)) +
  geom_line(aes(colour = group)) + 
  geom_ribbon(aes(ymin = conf.low, ymax = conf.high), alpha = 0.3, fill="grey70", linetype=0) + 
  scale_colour_manual(values = c("grey70", "black")) +  # 自定义线条颜色为灰色和黑色
  facet_wrap(~type, ncol = 2, scales = "free_x") +
  labs(  # 图表标题
       x = "Connection",                      # x轴标签
       y = "Environmental Information Disclosure",  # y轴标签
       colour = "Inspection Type",                   # 更新图例标题以更清晰表达
       fill = "Inspection Type")                     # 填充图例标题，确保与线条图例一致，可选

```
